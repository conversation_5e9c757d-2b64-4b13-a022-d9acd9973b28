import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { CigaSection } from "../sections/CigaSection";
import { DirectionManagementSection } from "../sections/DirecionManagementSection";
import { EmployeesSection } from "../sections/EmployeesSection";
import { ExpenditureSection } from "../sections/ExpenditureSection";
import { IncomeSection } from "../sections/IncomeSection";
import { PremisesSection } from "../sections/PremisesSection";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import type { BusinessSchemaType } from "~/lib/economic-substance/types/bahamas/business-schema";
import { businessSchema } from "~/lib/economic-substance/types/bahamas/business-schema";
import { getCigaDefaultValues } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { getDirectionManagementDefaultValues } from "~/lib/economic-substance/types/bahamas/direction-management-schema";
import { getEmployeesDefaultValues } from "~/lib/economic-substance/types/bahamas/employee-schema";
import { getExpenditureDefaultValues } from "~/lib/economic-substance/types/bahamas/expenditure-schema";
import { getIncomeDefaultValues } from "~/lib/economic-substance/types/bahamas/income-schema";
import { getPremisesDefaultValues } from "~/lib/economic-substance/types/bahamas/premises-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import type { PageSlug } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { decodeFields, encodeFields } from "~/lib/utilities/hashPrefix";

type Props = {
  pageSlug: PageSlug
}

export function BusinessForm({ pageSlug }: Props): ReactNode {
  const { submissionData } = useSubmission();
  const fetcher = useFetcher()
  const isSubmitting = fetcher.state === "submitting" || fetcher.state === "loading"
  const data = useMemo(() => {
    const rawData = submissionData[pageSlug] as BusinessSchemaType | undefined;
    if (!rawData) {
      return {} as BusinessSchemaType;
    }

    const premisesArray = Array.isArray(rawData?.premises) ? rawData.premises : [];
    const decodedPremises = premisesArray.map((premise) => {
      return decodeFields(premise, ["addressLine1", "addressLine2"]);
    });

    return { ...rawData, premises: decodedPremises };
  }, [submissionData, pageSlug]);
  const businessFormDefaultValues = useMemo(() => (
    {
      ...getIncomeDefaultValues(data),
      ...getExpenditureDefaultValues(data),
      ...getEmployeesDefaultValues(data),
      ...getPremisesDefaultValues(data),
      ...getDirectionManagementDefaultValues(data),
      ...getCigaDefaultValues(data),
    }
  ), [data])
  const form = useForm<BusinessSchemaType>({
    resolver: zodResolver(businessSchema),
    defaultValues: businessFormDefaultValues,
    shouldFocusError: false,
  });
  const { formState, reset } = form;
  const [canFocus, setCanFocus] = useState(true)

  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: BusinessSchemaType): void {
    const encodedPremises = data.premises?.map((premise) => {
      return encodeFields(premise, ["addressLine1", "addressLine2"]);
    });
    const encodedData: BusinessSchemaType = {
      ...data,
      premises: encodedPremises,
    };

    fetcher.submit({ data: JSON.stringify(encodedData) }, {
      method: "post",
    });
  }

  // Reset form when data changes
  useEffect(() => {
    reset(businessFormDefaultValues, { keepDefaultValues: true }); // Reset form values when data changes
  }, [data, reset, businessFormDefaultValues]);

  return (
    <div className="relative">
      <FormProvider {...form}>
        <Form {...form}>
          <RemixForm
            onSubmit={form.handleSubmit(onSubmit, onError)}
            noValidate
            id={ECONOMIC_SUBSTANCE_FORM_ID}
            className="space-y-5"
          >
            <div className="flex-col space-y-7">
              <IncomeSection />
              <ExpenditureSection />
              <EmployeesSection />
              <PremisesSection />
              <DirectionManagementSection />
              <CigaSection />
            </div>
          </RemixForm>
        </Form>
      </FormProvider>
      <LoadingState
        isLoading={isSubmitting}
        message="Saving..."
      />
    </div>
  )
}
