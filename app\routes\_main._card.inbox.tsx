import { <PERSON>alog, DialogContent, ScrollArea, ScrollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Outlet, json, useLoaderData, useNavigate } from "@remix-run/react";
import { MailSearch } from "lucide-react";
import { useContext, useState } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { formatDate, getTimezoneFromJurisdiction } from "~/lib/utilities/format";
import { SessionContext } from "~/components/session-context";
import InboxMessageDetails from "~/routes/_main._card.inbox.$id";
import type { InboxMessageListItemDTOPaginatedResponse } from "~/services/api-generated";
import { getInboxMessages } from "~/services/api-generated";
import { getPaginationParams } from "~/lib/utilities/get-pagination-params";
import { Pagination } from "~/components/ui/filters/Pagination";

const title = "Inbox" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Inbox",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<InboxMessageListItemDTOPaginatedResponse>> {
  await middleware(["auth", "terms", "requireAnnouncements"], request);
  const { pageNumber, pageSize } = await getPaginationParams({ request });
  const { data: inboxMessages, error } = await getInboxMessages({
    headers: await authHeaders(request),
    query: {
      pageNumber,
      pageSize,
    },
  });

  if (error) {
    throw new Response("Messages cannot be displayed", { status: 500 });
  }

  return json(inboxMessages);
}

export default function Inbox(): JSX.Element {
  const navigate = useNavigate();
  const { data, totalItemCount } = useLoaderData<typeof loader>() || { data: [] };
  const { company } = useContext(SessionContext);
  const timezone = getTimezoneFromJurisdiction(company?.jurisdictionName);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const closeModal = (): void => {
    setSelectedMessageId(null);
  };

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="mt-4">
        {data?.length === 0
          ? (
              <CenteredMessage title="No messages found." IconComponent={MailSearch}></CenteredMessage>
            )
          : (
              <>
                <ScrollArea>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>New</TableHead>
                        <TableHead>Received at</TableHead>
                        <TableHead>Subject</TableHead>
                        <TableHead>Master Client</TableHead>
                        <TableHead>Jurisdiction</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data && data.length > 0 && data?.map(message => (
                        <TableRow key={message.id} onClick={() => navigate(`/inbox/${message.id}`)}>
                          <TableCell>{message.fromUserName}</TableCell>
                          <TableCell>{formatDate(message.createdAt, { timezone })}</TableCell>
                          <TableCell>{message.subject}</TableCell>
                          <TableCell>{message?.masterClients}</TableCell>
                          <TableCell>{message?.jurisdictions}</TableCell>
                          <TableCell>{message.isRead ? "Read" : "Unread"}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <ScrollBar orientation="horizontal" />
                </ScrollArea>
                <Pagination totalItems={totalItemCount as number} />
              </>
            )}
        <Dialog modal open={!!selectedMessageId} onOpenChange={closeModal}>
          <DialogContent className="flex min-w-[800px]">
            {selectedMessageId && <InboxMessageDetails />}
          </DialogContent>
        </Dialog>
      </div>
      <Outlet />
    </div>
  );
}
