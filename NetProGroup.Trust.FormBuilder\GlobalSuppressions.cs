// <copyright file="GlobalSuppressions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.
using System.Diagnostics.CodeAnalysis;

#pragma warning disable IDE0076 // Invalid global 'SuppressMessageAttribute'
[assembly: SuppressMessage("Performance", "CA1825:Avoid zero-length array allocations", Justification = "Reviewed", Scope = "type")]
[assembly: SuppressMessage("StyleCop.CSharp.SpacingRules", "SA1000:Keywords should be spaced correctly", Justification = "Conflict in editor", Scope = "member")]
[assembly: SuppressMessage("Naming", "CA1711:Identifiers should not have incorrect suffix", Justification = "Reviewed", Scope = "type")]
[assembly: SuppressMessage("Naming", "CA1707:Identifiers should not contain underscores", Justification = "Allowed", Scope = "type")]
[assembly: SuppressMessage("Style", "IDE0071:Simplify interpolation", Justification = "Reviewed", Scope = "module")]
[assembly: SuppressMessage("Design", "CA1002:Do not expose generic lists", Justification = "Reviewed", Scope = "module")]
[assembly: SuppressMessage("Usage", "CA2227:Collection properties should be read only", Justification = "Reviewed", Scope = "module")]
[assembly: SuppressMessage("Globalization", "CA1305:Specify IFormatProvider", Justification = "Reviewed", Scope = "module")]
[assembly: SuppressMessage("Globalization", "CA1304:Specify CultureInfo", Justification = "Using default behaviour", Scope = "module")]
[assembly: SuppressMessage("Globalization", "CA1311:Specify a culture or use an invariant version", Justification = "Using default behaviour", Scope = "module")]


#pragma warning restore IDE0076 // Invalid global 'SuppressMessageAttribute'
