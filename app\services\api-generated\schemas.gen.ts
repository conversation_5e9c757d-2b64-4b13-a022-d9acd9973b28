// This file is auto-generated by @hey-api/openapi-ts

export const APIExceptionModelSchema = {
    type: 'object',
    properties: {
        code: {
            type: 'integer',
            format: 'int32'
        },
        error: {
            type: 'string',
            nullable: true
        },
        exceptionMessage: {
            type: 'string',
            nullable: true
        },
        innerExceptionMessages: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AcceptTermsConditionsDTOSchema = {
    type: 'object',
    properties: {
        version: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AcceptTermsConditionsResultDTOSchema = {
    type: 'object',
    properties: {
        success: {
            type: 'boolean'
        },
        acceptedAt: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const ActivityLogItemDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        actionDate: {
            type: 'string',
            format: 'date-time'
        },
        userName: {
            type: 'string',
            nullable: true
        },
        emailAddress: {
            type: 'string',
            nullable: true
        },
        activityType: {
            type: 'string',
            nullable: true
        },
        action: {
            type: 'string',
            nullable: true
        },
        shortDescription: {
            type: 'string',
            nullable: true
        },
        text: {
            type: 'string',
            nullable: true
        },
        entityName: {
            type: 'string',
            nullable: true
        },
        audits: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AuditUnitOfWorkDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ActivityLogItemDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ActivityLogItemDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AddActivityLogDTOSchema = {
    type: 'object',
    properties: {
        activityType: {
            type: 'string',
            nullable: true
        },
        shortDescription: {
            type: 'string',
            nullable: true
        },
        text: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AllSubmissionYearsDTOSchema = {
    type: 'object',
    properties: {
        moduleId: {
            type: 'string',
            format: 'uuid'
        },
        years: {
            type: 'array',
            items: {
                type: 'integer',
                format: 'int32'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AnnouncementDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        subject: {
            type: 'string',
            nullable: true
        },
        emailSubject: {
            type: 'string',
            nullable: true
        },
        body: {
            type: 'string',
            nullable: true
        },
        isSent: {
            type: 'boolean'
        },
        sendAt: {
            type: 'string',
            format: 'date-time'
        },
        sentAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        masterClientIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        masterClientCodes: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        legalEntityIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        jurisdictionIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        jurisdictionNames: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        userIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        documents: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AnnouncementDocumentDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AnnouncementDocumentDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        documentId: {
            type: 'string',
            format: 'uuid'
        },
        document: {
            '$ref': '#/components/schemas/DocumentDTO'
        },
        announcementId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const AnnouncementStatusSchema = {
    enum: ['Draft', 'Scheduled', 'Sent'],
    type: 'string'
} as const;

export const AnnualFeeDTOSchema = {
    type: 'object',
    properties: {
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        isPaid: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const AppVersionDTOSchema = {
    type: 'object',
    properties: {
        version: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ApplicationUserDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        surname: {
            type: 'string',
            nullable: true
        },
        username: {
            type: 'string',
            nullable: true
        },
        displayName: {
            type: 'string',
            nullable: true
        },
        email: {
            type: 'string',
            nullable: true
        },
        roleNames: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        roleIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        isActive: {
            type: 'boolean'
        },
        isBlocked: {
            type: 'boolean'
        },
        applicationUserRoles: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ApplicationUserRoleDTO'
            },
            nullable: true
        },
        objectId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ApplicationUserRoleDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        roleId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionStartResponseSchema = {
    type: 'object',
    properties: {
        data: {
            '$ref': '#/components/schemas/AttributeCollectionStartResponse_Data'
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionStartResponse_ActionSchema = {
    type: 'object',
    properties: {
        '@odata.type': {
            type: 'string',
            nullable: true
        },
        message: {
            type: 'string',
            nullable: true
        },
        inputs: {
            '$ref': '#/components/schemas/AttributeCollectionStartResponse_Inputs'
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionStartResponse_DataSchema = {
    type: 'object',
    properties: {
        '@odata.type': {
            type: 'string',
            nullable: true
        },
        actions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AttributeCollectionStartResponse_Action'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionStartResponse_InputsSchema = {
    type: 'object',
    properties: {
        displayName: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionSubmitResponseSchema = {
    type: 'object',
    properties: {
        data: {
            '$ref': '#/components/schemas/AttributeCollectionSubmitResponse_Data'
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionSubmitResponse_ActionSchema = {
    type: 'object',
    properties: {
        '@odata.type': {
            type: 'string',
            nullable: true
        },
        message: {
            type: 'string',
            nullable: true
        },
        attributes: {
            '$ref': '#/components/schemas/AttributeCollectionSubmitResponse_Attribute'
        },
        attributeErrors: {
            '$ref': '#/components/schemas/AttributeCollectionSubmitResponse_AttributeError'
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionSubmitResponse_AttributeSchema = {
    type: 'object',
    properties: {
        displayName: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionSubmitResponse_AttributeErrorSchema = {
    type: 'object',
    properties: {
        city: {
            type: 'string',
            nullable: true
        },
        displayName: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AttributeCollectionSubmitResponse_DataSchema = {
    type: 'object',
    properties: {
        '@odata.type': {
            type: 'string',
            nullable: true
        },
        actions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AttributeCollectionSubmitResponse_Action'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AuditUnitOfWorkDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        changedAt: {
            type: 'string',
            format: 'date-time'
        },
        changedBy: {
            maxLength: 50,
            minLength: 0,
            type: 'string',
            nullable: true
        },
        contextId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        entities: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AuditUnitOfWorkEntityDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AuditUnitOfWorkDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AuditUnitOfWorkDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AuditUnitOfWorkDetailDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        column: {
            type: 'string',
            nullable: true
        },
        oldValue: {
            type: 'string',
            nullable: true
        },
        newValue: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AuditUnitOfWorkEntityDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        entityName: {
            type: 'string',
            nullable: true
        },
        entityId: {
            type: 'string',
            nullable: true
        },
        action: {
            type: 'string',
            nullable: true
        },
        details: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AuditUnitOfWorkDetailDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const AvailableSubmissionYearsDTOSchema = {
    type: 'object',
    properties: {
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        moduleId: {
            type: 'string',
            format: 'uuid'
        },
        years: {
            type: 'array',
            items: {
                type: 'integer',
                format: 'int32'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const BadRequestResponseTypesSchema = {
    type: 'object',
    properties: {
        value: {
            nullable: true,
            readOnly: true
        },
        index: {
            type: 'integer',
            format: 'int32',
            readOnly: true
        },
        isT0: {
            type: 'boolean',
            readOnly: true
        },
        isT1: {
            type: 'boolean',
            readOnly: true
        },
        asT0: {
            '$ref': '#/components/schemas/ValidationProblemDetails'
        },
        asT1: {
            '$ref': '#/components/schemas/APIExceptionModel'
        }
    },
    additionalProperties: false,
    description: 'Represents the response types for a bad request, for Swagger documentation.'
} as const;

export const BeneficialOwnerComparisonDTOSchema = {
    type: 'object',
    properties: {
        currentVersion: {
            '$ref': '#/components/schemas/BeneficialOwnerDTO'
        },
        priorVersion: {
            '$ref': '#/components/schemas/BeneficialOwnerDTO'
        }
    },
    additionalProperties: false
} as const;

export const BeneficialOwnerDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        uniqueRelationCode: {
            type: 'string',
            nullable: true
        },
        isIndividual: {
            type: 'boolean'
        },
        officerTypeCode: {
            type: 'string',
            nullable: true
        },
        officerTypeName: {
            type: 'string',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        dateOfBirth: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        placeOfBirth: {
            type: 'string',
            nullable: true
        },
        countryOfBirth: {
            type: 'string',
            nullable: true
        },
        countryCodeOfBirth: {
            type: 'string',
            nullable: true
        },
        nationality: {
            type: 'string',
            nullable: true
        },
        residentialAddress: {
            type: 'string',
            nullable: true
        },
        incorporationNumber: {
            type: 'string',
            nullable: true
        },
        dateOfIncorporation: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        address: {
            type: 'string',
            nullable: true
        },
        countryOfFormation: {
            type: 'string',
            nullable: true
        },
        jurisdictionOfRegulator: {
            type: 'string',
            nullable: true
        },
        nameOfRegulator: {
            type: 'string',
            nullable: true
        },
        sovereignState: {
            type: 'string',
            nullable: true
        },
        tin: {
            type: 'string',
            nullable: true
        },
        stockCode: {
            type: 'string',
            nullable: true
        },
        stockExchange: {
            type: 'string',
            nullable: true
        },
        metaData: {
            '$ref': '#/components/schemas/LegalEntityRelationMetaData'
        }
    },
    additionalProperties: false
} as const;

export const BlockUserDTOSchema = {
    required: ['isBlocked'],
    type: 'object',
    properties: {
        isBlocked: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const BoDirDataStatusSchema = {
    enum: ['Initial', 'Refreshed', 'Confirmed', 'PendingUpdateRequest', 'Subsequent'],
    type: 'string'
} as const;

export const BoDirItemDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        productionOffice: {
            type: 'string',
            nullable: true
        },
        referralOffice: {
            type: 'string',
            nullable: true
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        vpEntityNumber: {
            type: 'string',
            nullable: true
        },
        entityPortalCode: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        directorVPCode: {
            type: 'string',
            nullable: true
        },
        directorName: {
            type: 'string',
            nullable: true
        },
        position: {
            type: 'string',
            nullable: true
        },
        directorType: {
            type: 'string',
            nullable: true
        },
        officerType: {
            type: 'string',
            nullable: true
        },
        specifics: {
            type: 'string',
            nullable: true
        },
        hasMissingInformation: {
            type: 'boolean'
        },
        hasBoDirInformation: {
            type: 'boolean'
        },
        status: {
            '$ref': '#/components/schemas/LegalEntityRelationStatus'
        },
        requestUpdateDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        confirmedDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        isIndividual: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const BoDirItemDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/BoDirItemDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const BoDirPositionSchema = {
    enum: ['Director', 'BeneficialOwner'],
    type: 'string'
} as const;

export const BoDirSpecificsSchema = {
    enum: ['BoDirInformation', 'MissingInformation', 'NoBoDirInformation'],
    type: 'string'
} as const;

export const CompaniesSearchResultDTOSchema = {
    type: 'object',
    properties: {
        companyId: {
            type: 'string',
            format: 'uuid'
        },
        companyName: {
            type: 'string',
            nullable: true
        },
        incorporationNumber: {
            type: 'string',
            nullable: true
        },
        isActive: {
            type: 'boolean'
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        jurisdictionName: {
            type: 'string',
            nullable: true
        },
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        entityType: {
            type: 'string',
            nullable: true
        },
        vpEntityStatus: {
            type: 'string',
            nullable: true
        },
        incorporationDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CompaniesSearchResultsDTOSchema = {
    type: 'object',
    properties: {
        companies: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompaniesSearchResultDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CompanyAnnualFeesDTOSchema = {
    type: 'object',
    properties: {
        companyId: {
            type: 'string',
            format: 'uuid'
        },
        annualFees: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AnnualFeeDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CompanyDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        jurisdictionName: {
            type: 'string',
            nullable: true
        },
        code: {
            type: 'string',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        vpEntityStatus: {
            type: 'string',
            nullable: true
        },
        entityType: {
            type: 'string',
            nullable: true
        },
        incorporationNumber: {
            type: 'string',
            nullable: true
        },
        isActive: {
            type: 'boolean'
        },
        legacyCode: {
            type: 'string',
            nullable: true
        },
        incorporationDate: {
            type: 'string',
            format: 'date-time'
        },
        referralOffice: {
            type: 'string',
            nullable: true
        },
        onboardingStatus: {
            '$ref': '#/components/schemas/OnboardingStatus'
        },
        previouslyDeclined: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const CompanyDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CompanyFinancialYearDtoSchema = {
    type: 'object',
    properties: {
        companyVPCode: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const CompanyModuleDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        key: {
            type: 'string',
            nullable: true
        },
        isActive: {
            type: 'boolean'
        },
        isEnabled: {
            type: 'boolean'
        },
        isApproved: {
            type: 'boolean'
        },
        jurisdictionIsEnabled: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const CompanyWithAnnualFeeStatusSearchResultDTOSchema = {
    type: 'object',
    properties: {
        companyId: {
            type: 'string',
            format: 'uuid'
        },
        companyName: {
            type: 'string',
            nullable: true
        },
        companyLegacyCode: {
            type: 'string',
            nullable: true
        },
        companyCode: {
            type: 'string',
            nullable: true
        },
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        dateSubmissionCreated: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        dateSubmissionSubmitted: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        isPaid: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const CompanyWithAnnualFeeStatusSearchResultDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyWithAnnualFeeStatusSearchResultDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CompleteRequestForInformationDTOSchema = {
    type: 'object',
    properties: {
        response: {
            type: 'string',
            nullable: true
        },
        includeAttachments: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const CompleteRequestForInformationManagementDTOSchema = {
    type: 'object',
    properties: {
        submittedToRegulator: {
            type: 'string',
            format: 'date-time'
        },
        remark: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ConfirmMFAResetResultDTOSchema = {
    type: 'object',
    properties: {
        confirmationCode: {
            type: 'string',
            nullable: true
        },
        success: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const ConsumerModelSchema = {
    type: 'object',
    properties: {
        applicationId: {
            type: 'string',
            format: 'uuid'
        },
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateCurrencyDTOSchema = {
    type: 'object',
    properties: {
        name: {
            type: 'string',
            nullable: true
        },
        code: {
            type: 'string',
            nullable: true
        },
        symbol: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateFormTemplateVersionDTOSchema = {
    type: 'object',
    properties: {
        formTemplateId: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        version: {
            type: 'string',
            nullable: true
        },
        startDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        year: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        formBuilder: {
            '$ref': '#/components/schemas/FormBuilder'
        }
    },
    additionalProperties: false
} as const;

export const CreateMasterClientUserDTOSchema = {
    type: 'object',
    properties: {
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        emailAddress: {
            type: 'string',
            format: 'email',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateOrUpdateTaxRateDTOSchema = {
    type: 'object',
    properties: {
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        taxRate: {
            type: 'number',
            format: 'double'
        },
        startDate: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const CreatePaymentRequestDTOSchema = {
    required: ['currencyId', 'legalEntityId'],
    type: 'object',
    properties: {
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        currencyId: {
            type: 'string',
            format: 'uuid'
        },
        invoiceIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateRFIDTOSchema = {
    type: 'object',
    properties: {
        submissionId: {
            type: 'string',
            format: 'uuid'
        },
        deadLine: {
            type: 'string',
            format: 'date-time'
        },
        comments: {
            type: 'string',
            nullable: true
        },
        includeAttachments: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const CreateTransactionRequestDTOSchema = {
    required: ['email', 'firstName', 'lastName'],
    type: 'object',
    properties: {
        paymentId: {
            type: 'string',
            format: 'uuid'
        },
        description: {
            maxLength: 500,
            minLength: 0,
            type: 'string',
            nullable: true
        },
        orderId: {
            maxLength: 50,
            minLength: 0,
            type: 'string',
            nullable: true
        },
        paymentRedirectUrl: {
            type: 'string',
            format: 'uri',
            nullable: true
        },
        cancelUrl: {
            type: 'string',
            format: 'uri',
            nullable: true
        },
        companyName: {
            type: 'string',
            nullable: true
        },
        firstName: {
            maxLength: 100,
            minLength: 0,
            type: 'string'
        },
        lastName: {
            maxLength: 100,
            minLength: 0,
            type: 'string'
        },
        email: {
            minLength: 1,
            type: 'string',
            format: 'email'
        },
        phoneNumber: {
            type: 'string',
            format: 'tel',
            nullable: true
        },
        merchantEmail: {
            type: 'string',
            format: 'email',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateTransactionResponseDTOSchema = {
    type: 'object',
    properties: {
        paymentProcessorResponse: {
            '$ref': '#/components/schemas/StartPaymentResponse'
        }
    },
    additionalProperties: false
} as const;

export const CreateUpdateAnnouncementDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        subject: {
            type: 'string',
            nullable: true
        },
        emailSubject: {
            type: 'string',
            nullable: true
        },
        body: {
            type: 'string',
            nullable: true
        },
        includeAttachments: {
            type: 'boolean'
        },
        sendNow: {
            type: 'boolean'
        },
        sendAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        sendToAllMasterClients: {
            type: 'boolean'
        },
        sendToActiveMasterClients: {
            type: 'boolean'
        },
        sendToAllJurisdictions: {
            type: 'boolean'
        },
        masterClientCodes: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        legalEntityIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        userIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CreateUserDTOSchema = {
    required: ['displayName', 'email', 'firstName', 'lastName'],
    type: 'object',
    properties: {
        firstName: {
            minLength: 1,
            type: 'string'
        },
        lastName: {
            minLength: 1,
            type: 'string'
        },
        displayName: {
            minLength: 1,
            type: 'string'
        },
        email: {
            minLength: 1,
            type: 'string',
            format: 'email'
        }
    },
    additionalProperties: false
} as const;

export const CurrencyDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        code: {
            type: 'string',
            nullable: true
        },
        symbol: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const CurrencyDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CurrencyDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const DataMigrationDTOSchema = {
    type: 'object',
    properties: {
        region: {
            type: 'string',
            nullable: true
        },
        lastUpdated: {
            type: 'string',
            format: 'date-time'
        },
        initialSyncCompleted: {
            type: 'boolean'
        },
        migrationCompleted: {
            type: 'boolean'
        },
        status: {
            '$ref': '#/components/schemas/MigrationStatus'
        },
        stopRequested: {
            type: 'boolean'
        },
        unprocessedRecords: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/UnprocessedRecordDTO'
            },
            nullable: true
        },
        entityMigrationProgresses: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/EntityMigrationProgressDTO'
            },
            nullable: true
        },
        error: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const DeclineCompanyDTOSchema = {
    type: 'object',
    properties: {
        declineReason: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const DirectorComparisonDTOSchema = {
    type: 'object',
    properties: {
        currentVersion: {
            '$ref': '#/components/schemas/DirectorDTO'
        },
        priorVersion: {
            '$ref': '#/components/schemas/DirectorDTO'
        }
    },
    additionalProperties: false
} as const;

export const DirectorDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        uniqueRelationCode: {
            type: 'string',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        isIndividual: {
            type: 'boolean'
        },
        officerTypeName: {
            type: 'string',
            nullable: true
        },
        directorType: {
            type: 'string',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        formerName: {
            type: 'string',
            nullable: true
        },
        dateOfBirth: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        placeOfBirth: {
            type: 'string',
            nullable: true
        },
        countryOfBirth: {
            type: 'string',
            nullable: true
        },
        countryCodeOfBirth: {
            type: 'string',
            nullable: true
        },
        nationality: {
            type: 'string',
            nullable: true
        },
        tin: {
            type: 'string',
            nullable: true
        },
        residentialAddress: {
            type: 'string',
            nullable: true
        },
        serviceAddress: {
            type: 'string',
            nullable: true
        },
        address: {
            type: 'string',
            nullable: true
        },
        incorporationNumber: {
            type: 'string',
            nullable: true
        },
        appointmentDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        cessationDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        dateOfIncorporation: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        incorporationPlace: {
            type: 'string',
            nullable: true
        },
        incorporationCountry: {
            type: 'string',
            nullable: true
        },
        incorporationCountryCode: {
            type: 'string',
            nullable: true
        },
        metaData: {
            '$ref': '#/components/schemas/LegalEntityRelationMetaData'
        }
    },
    additionalProperties: false
} as const;

export const DocumentDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        type: {
            type: 'integer',
            format: 'int32'
        },
        filename: {
            type: 'string',
            nullable: true
        },
        description: {
            type: 'string',
            nullable: true
        },
        fileSize: {
            type: 'integer',
            format: 'int64'
        },
        documentData: {
            type: 'string',
            format: 'byte',
            nullable: true
        },
        addedAt: {
            type: 'string',
            format: 'date-time'
        },
        hash: {
            type: 'string',
            nullable: true
        },
        expirationDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const DocumentTypeSchema = {
    enum: ['Unknown', 'Xls', 'Pdf', 'Image'],
    type: 'string'
} as const;

export const EntityMigrationProgressDTOSchema = {
    type: 'object',
    properties: {
        entityName: {
            type: 'string',
            nullable: true
        },
        sourceCount: {
            type: 'integer',
            format: 'int32'
        },
        processedCount: {
            type: 'integer',
            format: 'int32'
        },
        successCount: {
            type: 'integer',
            format: 'int32'
        },
        failedCount: {
            type: 'integer',
            format: 'int32'
        },
        lastUpdated: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const ExportSubmissionDTOSchema = {
    required: ['financialYear', 'jurisdiction', 'module', 'submissionIds'],
    type: 'object',
    properties: {
        submissionIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            }
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        jurisdiction: {
            minLength: 1,
            type: 'string'
        },
        module: {
            minLength: 1,
            type: 'string'
        }
    },
    additionalProperties: false
} as const;

export const FeatureFlagDTOSchema = {
    type: 'object',
    properties: {
        name: {
            type: 'string',
            nullable: true
        },
        isEnabled: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const FeeSettingsDTOSchema = {
    type: 'object',
    properties: {
        strSubmissionFee: {
            type: 'number',
            format: 'double',
            nullable: true
        },
        strSubmissionFeeInvoiceText: {
            type: 'string',
            nullable: true
        },
        strSubmissionLatePaymentFeeExempt: {
            type: 'boolean',
            nullable: true
        },
        bfrSubmissionFee: {
            type: 'number',
            format: 'double',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const FormBaseSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            nullable: true
        },
        createdBy: {
            type: 'string',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        description: {
            type: 'string',
            nullable: true
        },
        version: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const FormBuilderSchema = {
    type: 'object',
    properties: {
        form: {
            '$ref': '#/components/schemas/FormBase'
        }
    },
    additionalProperties: false
} as const;

export const FormDocumentRevisionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        revision: {
            type: 'integer',
            format: 'int32'
        },
        status: {
            '$ref': '#/components/schemas/FormDocumentRevisionStatus'
        },
        statusText: {
            type: 'string',
            nullable: true
        },
        formBuilder: {
            '$ref': '#/components/schemas/FormBuilder'
        }
    },
    additionalProperties: false
} as const;

export const FormDocumentRevisionKeyValueDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        revision: {
            type: 'integer',
            format: 'int32'
        },
        status: {
            '$ref': '#/components/schemas/FormDocumentRevisionStatus'
        },
        statusText: {
            type: 'string',
            nullable: true
        },
        formBuilder: {
            '$ref': '#/components/schemas/KeyValueFormBuilderDTO'
        }
    },
    additionalProperties: false,
    description: `Version of NetProGroup.Trust.Application.Contracts.Forms.FormDocumentRevisionDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.KeyValueFormBuilderDTO form document.
Intended to generate correct OpenAPI documentation.`
} as const;

export const FormDocumentRevisionStatusSchema = {
    enum: ['Draft', 'Finalized'],
    type: 'string'
} as const;

export const FormDocumentStatusSchema = {
    enum: ['Draft', 'Revision', 'Finalized'],
    type: 'string'
} as const;

export const FormDocumentWithRevisionsDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/FormDocumentStatus'
        },
        statusText: {
            type: 'string',
            nullable: true
        },
        revisions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/FormDocumentRevisionDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const FormDocumentWithRevisionsKeyValueDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/FormDocumentStatus'
        },
        statusText: {
            type: 'string',
            nullable: true
        },
        revisions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/FormDocumentRevisionKeyValueDTO'
            },
            description: `Gets or sets the collection of revisions for the document.
Override of the type of the revisions, to display the correct type in OpenAPI documentation.`,
            nullable: true
        }
    },
    additionalProperties: false,
    description: `Version of NetProGroup.Trust.Application.Contracts.Forms.FormDocumentWithRevisionsDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.FormDocumentRevisionKeyValueDTO form document.
Intended to generate correct OpenAPI documentation.`
} as const;

export const FormTemplateDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        jurisdictionName: {
            type: 'string',
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid'
        },
        moduleName: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const FormTemplateVersionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        version: {
            type: 'string',
            nullable: true
        },
        startAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        formBuilder: {
            '$ref': '#/components/schemas/FormBuilder'
        }
    },
    additionalProperties: false
} as const;

export const FormTemplateWithVersionsDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        jurisdictionName: {
            type: 'string',
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid'
        },
        moduleName: {
            type: 'string',
            nullable: true
        },
        versions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/FormTemplateVersionDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const GetSubmissionsPaidStatusRequestDTOSchema = {
    type: 'object',
    properties: {
        companyFilingYears: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyFinancialYearDto'
            },
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const GetUserMFAMethodDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        mfaMethod: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InboxAttachmentDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        filename: {
            type: 'string',
            nullable: true
        },
        description: {
            type: 'string',
            nullable: true
        },
        fileSize: {
            type: 'integer',
            format: 'int64'
        }
    },
    additionalProperties: false
} as const;

export const InboxInfoDTOSchema = {
    type: 'object',
    properties: {
        totalMessages: {
            type: 'integer',
            format: 'int32'
        },
        totalUnreadMessages: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const InboxMessageDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        fromUserId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        fromUserName: {
            type: 'string',
            nullable: true
        },
        toUserId: {
            type: 'string',
            format: 'uuid'
        },
        toUserName: {
            type: 'string',
            nullable: true
        },
        subject: {
            type: 'string',
            nullable: true
        },
        isRead: {
            type: 'boolean'
        },
        readAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        hasAttachments: {
            type: 'boolean'
        },
        masterClients: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        jurisdictions: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        legalEntities: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        users: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        body: {
            type: 'string',
            nullable: true
        },
        inboxAttachments: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InboxAttachmentDTO'
            },
            nullable: true,
            readOnly: true
        },
        urlAttachments: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InboxMessageListItemDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        fromUserId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        fromUserName: {
            type: 'string',
            nullable: true
        },
        toUserId: {
            type: 'string',
            format: 'uuid'
        },
        toUserName: {
            type: 'string',
            nullable: true
        },
        subject: {
            type: 'string',
            nullable: true
        },
        isRead: {
            type: 'boolean'
        },
        readAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        hasAttachments: {
            type: 'boolean'
        },
        masterClients: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        jurisdictions: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        legalEntities: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        users: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InboxMessageListItemDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InboxMessageListItemDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InvoiceDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        invoiceNr: {
            type: 'string',
            nullable: true
        },
        companyName: {
            type: 'string',
            nullable: true
        },
        amount: {
            type: 'number',
            format: 'double'
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        incorporationNr: {
            type: 'string',
            nullable: true
        },
        file: {
            type: 'string',
            nullable: true
        },
        date: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/PaymentStatus'
        },
        currencySymbol: {
            type: 'string',
            nullable: true
        },
        paidDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        transactionId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        txId: {
            type: 'string',
            nullable: true
        },
        currencyId: {
            type: 'string',
            format: 'uuid'
        },
        layout: {
            type: 'string',
            nullable: true
        },
        invoiceLines: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InvoiceLineDTO'
            },
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        address1: {
            type: 'string',
            nullable: true
        },
        address2: {
            type: 'string',
            nullable: true
        },
        addressZipCode: {
            type: 'string',
            nullable: true
        },
        addressCity: {
            type: 'string',
            nullable: true
        },
        addressCountry: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InvoiceDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/InvoiceDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InvoiceLineDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        invoiceId: {
            type: 'string',
            format: 'uuid'
        },
        currencyId: {
            type: 'string',
            format: 'uuid'
        },
        description: {
            type: 'string',
            nullable: true
        },
        sequence: {
            type: 'integer',
            format: 'int32'
        },
        amount: {
            type: 'number',
            format: 'double'
        },
        articleNr: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const InvoicePaymentStatusSchema = {
    enum: ['UnPaid', 'Paid'],
    type: 'string'
} as const;

export const JurisdictionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        code: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const JurisdictionDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/JurisdictionDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const JurisdictionDocumentSettingsDTOSchema = {
    type: 'object',
    properties: {
        header: {
            type: 'string',
            nullable: true
        },
        footer: {
            type: 'string',
            nullable: true
        },
        companyInfo: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const JurisdictionTaxRateDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        jurisdictionId: {
            type: 'string',
            format: 'uuid'
        },
        taxRate: {
            type: 'number',
            format: 'double'
        },
        startDate: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const JurisdictionTaxRateDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/JurisdictionTaxRateDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const KeyValueFormSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            nullable: true
        },
        createdBy: {
            type: 'string',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        description: {
            type: 'string',
            nullable: true
        },
        version: {
            type: 'string',
            nullable: true
        },
        dataSet: {
            type: 'object',
            additionalProperties: {
                type: 'string',
                nullable: true
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const KeyValueFormBuilderDTOSchema = {
    type: 'object',
    properties: {
        form: {
            '$ref': '#/components/schemas/KeyValueForm'
        }
    },
    additionalProperties: false,
    description: 'Represents the top level of a document holding a polymorphic form.'
} as const;

export const LegalEntityRelationAssistanceRequestTypeSchema = {
    enum: ['None', 'NoBeneficialOwner', 'NoDirector', 'NoShareholder'],
    type: 'string'
} as const;

export const LegalEntityRelationMetaDataSchema = {
    type: 'object',
    properties: {
        status: {
            '$ref': '#/components/schemas/LegalEntityRelationStatus'
        },
        statusText: {
            type: 'string',
            nullable: true
        },
        receivedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        confirmedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        confirmedByUserId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        confirmedByUserName: {
            type: 'string',
            nullable: true
        },
        updateRequestedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        updateRequestedByUserId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        updateRequestedByUserName: {
            type: 'string',
            nullable: true
        },
        updateRequestType: {
            '$ref': '#/components/schemas/LegalEntityRelationUpdateRequestType'
        },
        updateRequestTypeName: {
            type: 'string',
            nullable: true
        },
        updateRequestComments: {
            type: 'string',
            nullable: true
        },
        missingDataFields: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const LegalEntityRelationStatusSchema = {
    enum: ['Initial', 'Refreshed', 'Confirmed', 'PendingUpdateRequest', 'UpdateReceived'],
    type: 'string'
} as const;

export const LegalEntityRelationUpdateRequestTypeSchema = {
    enum: ['None', 'MissingBeneficialOwners', 'MissingDirectors', 'MissingShareholders', 'ChangeOfBeneficialOwners', 'ChangeOfBeneficialOwnersAddress', 'ChangeOfBeneficialOwnersParticulars', 'ChangeOfDirectors', 'ChangeOfDirectorsAddress', 'ChangeOfDirectorsParticulars', 'ChangeOfShareholders', 'ChangeOfShareholdersAddress', 'ChangeOfShareholdersParticulars', 'OtherUpdateOfBeneficialOwners', 'OtherUpdateOfDirectors', 'OtherUpdateOfShareholders'],
    type: 'string'
} as const;

export const ListAnnouncementDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        subject: {
            type: 'string',
            nullable: true
        },
        body: {
            type: 'string',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/AnnouncementStatus'
        },
        sendAt: {
            type: 'string',
            format: 'date-time'
        },
        sentAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        masterClientIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        masterClientCodes: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        legalEntityIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        jurisdictionIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        jurisdictionNames: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        userIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListAnnouncementDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListAnnouncementDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListApplicationUsersDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        email: {
            type: 'string',
            nullable: true
        },
        isBlocked: {
            type: 'boolean'
        },
        primaryRoleLabel: {
            type: 'string',
            nullable: true
        },
        initialSyncAt: {
            type: 'string',
            nullable: true
        },
        syncStatus: {
            '$ref': '#/components/schemas/SyncStatus'
        },
        registrationDate: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListApplicationUsersDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListApplicationUsersDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListBeneficialOwnersDTOSchema = {
    type: 'object',
    properties: {
        beneficialOwners: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/BeneficialOwnerDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListCompanyModulesDTOSchema = {
    type: 'object',
    properties: {
        modules: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyModuleDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListDirectorsDTOSchema = {
    type: 'object',
    properties: {
        directors: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/DirectorDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListFormTemplatesDTOSchema = {
    type: 'object',
    properties: {
        formTemplates: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/FormTemplateDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListModulesDTOSchema = {
    type: 'object',
    properties: {
        modules: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ModuleDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionBahamasDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        createdByEmail: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        legalEntityVPCode: {
            type: 'string',
            nullable: true
        },
        legalEntityVPStatus: {
            type: 'string',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        submittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        initialSubmittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        exportedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        incorporationDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        incorporationCode: {
            type: 'string',
            nullable: true
        },
        paymentMethod: {
            type: 'string',
            nullable: true
        },
        paymentReference: {
            type: 'string',
            nullable: true
        },
        paymentReceivedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        financialPeriodStartsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        financialPeriodEndsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        legalEntityReferralOffice: {
            type: 'string',
            nullable: true
        },
        reopenRequestComments: {
            type: 'string',
            nullable: true
        },
        hasActivityNone: {
            type: 'boolean'
        },
        hasActivityHoldingBusiness: {
            type: 'boolean'
        },
        hasActivityFinanceLeasingBusiness: {
            type: 'boolean'
        },
        hasActivityBankingBusiness: {
            type: 'boolean'
        },
        hasActivityInsuranceBusiness: {
            type: 'boolean'
        },
        hasActivityFundManagementBusiness: {
            type: 'boolean'
        },
        hasActivityHeadquartersBusiness: {
            type: 'boolean'
        },
        hasActivityShippingBusiness: {
            type: 'boolean'
        },
        hasActivityIntellectualPropertyBusiness: {
            type: 'boolean'
        },
        requestForinformation: {
            type: 'string',
            nullable: true
        },
        requestForInformationStatus: {
            type: 'string',
            nullable: true
        },
        requestForInformationCompletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        isPaid: {
            type: 'boolean',
            nullable: true
        },
        isDeleted: {
            type: 'boolean'
        },
        deletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionBahamasDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListSubmissionBahamasDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        submittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        createdByEmail: {
            type: 'string',
            nullable: true
        },
        statusText: {
            type: 'string',
            nullable: true,
            deprecated: true
        },
        isPaid: {
            type: 'boolean',
            nullable: true
        },
        reportId: {
            type: 'string',
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        layout: {
            type: 'string',
            nullable: true
        },
        exportedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        legalEntityVPCode: {
            type: 'string',
            nullable: true
        },
        legalEntityReferralOffice: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        paymentMethod: {
            type: 'string',
            nullable: true
        },
        paymentReceivedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        paymentReference: {
            type: 'string',
            nullable: true
        },
        txId: {
            type: 'string',
            nullable: true
        },
        invoiceId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        isUsingAccountingRecordsTool: {
            type: 'string',
            nullable: true
        },
        financialPeriodStartsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        financialPeriodEndsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        lastActivityAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenRequestComments: {
            type: 'string',
            nullable: true
        },
        rfiDeadLine: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        legalEntityVPStatus: {
            type: 'string',
            nullable: true
        },
        legalEntityVPSubStatus: {
            type: 'string',
            nullable: true
        },
        incorporationNr: {
            type: 'string',
            nullable: true
        },
        moduleName: {
            type: 'string',
            nullable: true
        },
        isDeleted: {
            type: 'boolean'
        },
        deletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListSubmissionDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionRFIDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        financialPeriodStartsAt: {
            type: 'string',
            format: 'date-time'
        },
        financialPeriodEndsAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        rfiStatus: {
            '$ref': '#/components/schemas/RequestForInformationStatus'
        },
        exportedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        rfiCreatedAt: {
            type: 'string',
            format: 'date-time'
        },
        rfiDeadLine: {
            type: 'string',
            format: 'date-time'
        },
        rfiCompletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        rfiLastReminderSentAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListSubmissionRFIDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListSubmissionRFIDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ListUserDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        firstName: {
            type: 'string',
            nullable: true
        },
        lastName: {
            type: 'string',
            nullable: true
        },
        displayName: {
            type: 'string',
            nullable: true
        },
        email: {
            type: 'string',
            nullable: true
        },
        isRegistered: {
            type: 'boolean'
        },
        invitationDetails: {
            '$ref': '#/components/schemas/UserInvitationDetailsDTO'
        }
    },
    additionalProperties: false
} as const;

export const ListUserDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListUserDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MFAInfoDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        mfaMethod: {
            type: 'string',
            nullable: true
        },
        mfaIsEnabled: {
            type: 'boolean'
        },
        mfaAuthenticatorQRUrl: {
            type: 'string',
            nullable: true
        },
        mfaAuthenticatorSecret: {
            type: 'string',
            nullable: true
        },
        mfaEmailCodeExpiresAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        mfaEmailCodeExpiresIn: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidByCompanyAndYearResultDTOSchema = {
    type: 'object',
    properties: {
        companyVPCode: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        errorMessage: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidByCompanyYearResponseSchema = {
    type: 'object',
    properties: {
        results: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MarkSubmissionsAsPaidByCompanyAndYearResultDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidByCompanyYearsRequestDTOSchema = {
    type: 'object',
    properties: {
        companyFilingYears: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyFinancialYearDto'
            },
            nullable: true
        },
        isPaid: {
            type: 'boolean'
        },
        moduleId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidRequestDTOSchema = {
    type: 'object',
    properties: {
        submissionIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        isPaid: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidResponseSchema = {
    type: 'object',
    properties: {
        results: {
            type: 'object',
            additionalProperties: {
                '$ref': '#/components/schemas/MarkSubmissionsAsPaidResultDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MarkSubmissionsAsPaidResultDTOSchema = {
    type: 'object',
    properties: {
        errorMessage: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MasterClientDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        code: {
            type: 'string',
            nullable: true
        },
        jurisdictions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/JurisdictionDTO'
            },
            nullable: true
        },
        masterClientUsers: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListUserDTO'
            },
            nullable: true
        },
        masterClientManagers: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ListUserDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MasterClientDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MasterClientDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MasterClientUserDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        userId: {
            type: 'string',
            format: 'uuid'
        },
        isManuallyAdded: {
            type: 'boolean'
        },
        firstName: {
            type: 'string',
            nullable: true
        },
        lastName: {
            type: 'string',
            nullable: true
        },
        email: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MasterClientsSearchResultDTOSchema = {
    type: 'object',
    properties: {
        masterClientId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        companies: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/CompanyDTO'
            },
            nullable: true
        },
        hasActiveRequestsForInformation: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const MasterClientsSearchResultsDTOSchema = {
    type: 'object',
    properties: {
        masterClients: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/MasterClientsSearchResultDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const MigrationStatusSchema = {
    enum: ['NotStarted', 'InProgress', 'Completed', 'Failed', 'Cancelled'],
    type: 'string'
} as const;

export const ModuleDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        key: {
            type: 'string',
            nullable: true
        },
        isActive: {
            type: 'boolean',
            nullable: true
        },
        isEnabled: {
            type: 'boolean',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const OnboardingStatusSchema = {
    enum: ['Unknown', 'Onboarding', 'Approved', 'Declined', 'ClosedWhileOnboarding'],
    type: 'string'
} as const;

export const PCPApplicationUserDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        surname: {
            type: 'string',
            nullable: true
        },
        username: {
            type: 'string',
            nullable: true
        },
        displayName: {
            type: 'string',
            nullable: true
        },
        email: {
            type: 'string',
            nullable: true
        },
        roleNames: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        roleIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        isActive: {
            type: 'boolean'
        },
        isBlocked: {
            type: 'boolean'
        },
        applicationUserRoles: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ApplicationUserRoleDTO'
            },
            nullable: true
        },
        objectId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        permissions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/UserPermissionDTO'
            },
            nullable: true
        },
        primaryRoleLabel: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const PaymentDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        companyName: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        incorporationNr: {
            type: 'string',
            nullable: true
        },
        dateTime: {
            type: 'string',
            format: 'date-time'
        },
        currencyId: {
            type: 'string',
            format: 'uuid'
        },
        currencySymbol: {
            type: 'string',
            nullable: true
        },
        amount: {
            type: 'number',
            format: 'double'
        },
        status: {
            '$ref': '#/components/schemas/PaymentStatus'
        }
    },
    additionalProperties: false
} as const;

export const PaymentDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PaymentDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const PaymentDetailsResponseDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        currencyId: {
            type: 'string',
            format: 'uuid'
        },
        amount: {
            type: 'number',
            format: 'double'
        },
        status: {
            '$ref': '#/components/schemas/PaymentStatus'
        },
        paymentTransactions: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/PaymentTransactionResponseDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const PaymentStatusSchema = {
    enum: ['Pending', 'Completed', 'Failed', 'Refunded', 'Cancelled', 'InProgress', 'OnHold', 'Disputed', 'Decline'],
    type: 'string'
} as const;

export const PaymentTransactionResponseDTOSchema = {
    type: 'object',
    properties: {
        result: {
            type: 'string',
            nullable: true
        },
        resultCode: {
            type: 'string',
            nullable: true
        },
        resultMessage: {
            type: 'string',
            nullable: true
        },
        transactionId: {
            type: 'string',
            nullable: true
        },
        status: {
            type: 'string',
            nullable: true
        },
        processCreatedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        paidAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        isFinished: {
            type: 'boolean'
        },
        paymentProviderId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const PermissionDTOSchema = {
    type: 'object',
    properties: {
        permissionName: {
            enum: ['announcements.search', 'announcements.view', 'announcements.delete', 'announcements.create-limited', 'announcements.create', 'bfr.panama.submissions.view', 'bfr.panama.submissions.search', 'bfr.panama.submissions.export', 'bfr.panama.submissions.reset', 'bfr.panama.submissions.view-paid', 'bfr.panama.submissions.mark-paid', 'bfr.panama.invoices.export', 'bfr.panama.rfi-request.start', 'bo-dir.search', 'bo-dir.view', 'bo-dir.export', 'companies.onboarding.access', 'companies.onboarding.approve', 'companies.onboarding.reject', 'companies.search', 'companies.view', 'companies.modules.available.view', 'companies.modules.available.set', 'companies.custom-str-fee.view', 'companies.custom-str-fee.set', 'companies.late-payment-exemption-str.set', 'companies.custom-bfr-fee.view', 'companies.custom-bfr-fee.set', 'companies.annual-fee.view', 'companies.annual-fee.set', 'companies.log.view', 'companies.first-submission-year.view', 'companies.first-submission-year.set', 'companies.submissions.move-delete', 'companies.onboarding-status.reset', 'companies.str-submission.log.view', 'companies.inactive-to-file-submission.set', 'companies.delete-all', 'companies.delete-draft', 'es.bahamas.submissions.view', 'es.bahamas.submissions.search', 'es.bahamas.submissions.export', 'es.bahamas.submissions.reset', 'es.bahamas.submissions.delete-completed', 'es.bahamas.submissions.delete-saved', 'es.bahamas.submissions.view-paid', 'es.bahamas.submissions.mark-paid', 'es.bahamas.payments.import', 'es.bahamas.submissions.export.ita', 'es.bahamas.invoices.export', 'es.bahamas.companies.custom-es-fee.view', 'es.bahamas.companies.custom-es-fee.set', 'es.bahamas.financial-period.set', 'es.bahamas.data-migration', 'es.bahamas.rfi-request.start', 'es.bvi.submissions.view', 'es.bvi.submissions.search', 'es.bvi.submissions.export', 'es.bvi.submissions.reset', 'es.bvi.submissions.delete-completed', 'es.bvi.submissions.delete-saved', 'es.bvi.submissions.view-paid', 'es.bvi.submissions.mark-paid', 'es.bvi.payments.import', 'es.bvi.submissions.export.ita', 'es.bvi.invoices.export', 'es.bvi.rfi-request.start', 'es.bvi.companies.custom-es-fee.view', 'es.bvi.companies.custom-es-fee.set', 'masterclients.search', 'masterclients.view', 'masterclients.send-invitation', 'masterclients.trident-users.view', 'masterclients.trident-users.add', 'masterclients.trident-users.remove', 'masterclients.log.view', 'rfi.view', 'rfi.complete', 'rfi.start', 'rfi.cancel', 'general', 'general.status-page-sync', 'str.submissions.view', 'str.submissions.search', 'str.submissions.export', 'str.submissions.reset', 'str.submissions.view-paid', 'str.submissions.mark-paid', 'str.payments.import', 'str.submissions.export.ird', 'str.invoices.export', 'str.invoices.export.financial-report', 'str.management-information', 'str.fee.view', 'str.fee.set', 'str.late-payments.view', 'str.late-payments.set', 'str.data-migration', 'str.late-payments.edit', 'str.submission-log.view', 'str.rfi-request.start', 'users.search', 'users.view', 'users.block', 'users.unblock', 'users.reset-authentication', 'users.view-log'],
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ProblemDetailsSchema = {
    type: 'object',
    properties: {
        type: {
            type: 'string',
            nullable: true
        },
        title: {
            type: 'string',
            nullable: true
        },
        status: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        detail: {
            type: 'string',
            nullable: true
        },
        instance: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: {}
} as const;

export const ProductionOfficeTypeSchema = {
    enum: ['THKO', 'TBVI', 'TCYP', 'TPANVG', 'TNEV'],
    type: 'string'
} as const;

export const ProviderModelSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ReopenSubmissionDTOSchema = {
    type: 'object',
    properties: {
        submissionId: {
            type: 'string',
            format: 'uuid'
        },
        comments: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ReportDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        reportName: {
            type: 'string',
            nullable: true
        },
        filename: {
            type: 'string',
            nullable: true
        },
        type: {
            type: 'string',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const ReportDTOPaginatedResponseSchema = {
    type: 'object',
    properties: {
        pageNumber: {
            type: 'integer',
            format: 'int32'
        },
        pageCount: {
            type: 'integer',
            format: 'int32'
        },
        pageSize: {
            type: 'integer',
            format: 'int32'
        },
        totalItemCount: {
            type: 'integer',
            format: 'int32'
        },
        hasPrevious: {
            type: 'boolean',
            readOnly: true
        },
        hasNext: {
            type: 'boolean',
            readOnly: true
        },
        data: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ReportDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ReportTypeSchema = {
    enum: ['Financial', 'CompaniesWithoutSubmissions', 'SubmissionsNotPaid', 'ContactsInfo', 'BasicFinancialReport', 'EconomicSubstance'],
    type: 'string'
} as const;

export const RequestAssistanceDTOSchema = {
    type: 'object',
    properties: {
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        assistanceRequestType: {
            '$ref': '#/components/schemas/LegalEntityRelationAssistanceRequestType'
        },
        assistanceRequestComments: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const RequestForInformationDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        masterClientName: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        jurisdictionCode: {
            type: 'string',
            nullable: true
        },
        jurisdictionName: {
            type: 'string',
            nullable: true
        },
        submissionId: {
            type: 'string',
            format: 'uuid'
        },
        deadLine: {
            type: 'string',
            format: 'date-time'
        },
        requestComment: {
            type: 'string',
            nullable: true
        },
        response: {
            type: 'string',
            nullable: true
        },
        remark: {
            type: 'string',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/RequestForInformationStatus'
        },
        completedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        repliedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        repliedBy: {
            type: 'string',
            nullable: true
        },
        completedBy: {
            type: 'string',
            nullable: true
        },
        createdBy: {
            type: 'string',
            nullable: true
        },
        documents: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionRFIDocumentDTO'
            },
            nullable: true,
            readOnly: true
        }
    },
    additionalProperties: false
} as const;

export const RequestForInformationStatusSchema = {
    enum: ['Draft', 'Active', 'Cancelled', 'Completed'],
    type: 'string'
} as const;

export const RequestMFAResetResultDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        mfaEmailCodeExpiresAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        mfaEmailCodeExpiresIn: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const RequestUpdateDTOSchema = {
    type: 'object',
    properties: {
        uniqueRelationId: {
            type: 'string',
            nullable: true
        },
        updateRequestType: {
            '$ref': '#/components/schemas/LegalEntityRelationUpdateRequestType'
        },
        updateRequestComments: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const STRLatePaymentFeeDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        description: {
            type: 'string',
            nullable: true
        },
        invoiceText: {
            type: 'string',
            nullable: true
        },
        startAt: {
            type: 'string',
            format: 'date-time'
        },
        endAt: {
            type: 'string',
            format: 'date-time'
        },
        financialYear: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        amount: {
            type: 'number',
            format: 'double'
        },
        currencyCode: {
            type: 'string',
            nullable: true
        },
        charge: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const STRLatePaymentFeeSettingsDTOSchema = {
    type: 'object',
    properties: {
        strLatePaymentFees: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/STRLatePaymentFeeDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SendEmailDTOSchema = {
    required: ['body', 'recipientEmailAddress', 'subject'],
    type: 'object',
    properties: {
        recipientEmailAddress: {
            minLength: 1,
            type: 'string',
            format: 'email'
        },
        subject: {
            minLength: 1,
            type: 'string'
        },
        body: {
            minLength: 1,
            type: 'string'
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SendInvitationUserDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientId: {
            type: 'string',
            format: 'uuid'
        }
    },
    additionalProperties: false
} as const;

export const SendInvitationsDTOSchema = {
    type: 'object',
    properties: {
        userIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        userMasterClients: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SendInvitationUserDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SetCompanyAnnualFeesDTOSchema = {
    type: 'object',
    properties: {
        annualFees: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/AnnualFeeDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SetCompanyModuleDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        isEnabled: {
            type: 'boolean'
        },
        isApproved: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const SetCompanyModulesDTOSchema = {
    type: 'object',
    properties: {
        modules: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SetCompanyModuleDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SetModuleDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        isEnabled: {
            type: 'boolean',
            nullable: true
        },
        isApproved: {
            type: 'boolean',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SetModulesDTOSchema = {
    type: 'object',
    properties: {
        modules: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SetModuleDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SetUserMFAMethodDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        mfaMethod: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SettingsDTOSchema = {
    type: 'object',
    properties: {
        jurisdictionDocumentSettings: {
            '$ref': '#/components/schemas/JurisdictionDocumentSettingsDTO'
        },
        feeSettings: {
            '$ref': '#/components/schemas/FeeSettingsDTO'
        },
        strLatePaymentFeeSettings: {
            '$ref': '#/components/schemas/STRLatePaymentFeeSettingsDTO'
        },
        submissionSettings: {
            '$ref': '#/components/schemas/SubmissionSettingsDTO'
        },
        logs: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ActivityLogItemDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ShareholderDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        uniqueRelationCode: {
            type: 'string',
            nullable: true
        },
        isIndividual: {
            type: 'boolean'
        },
        officerTypeCode: {
            type: 'string',
            nullable: true
        },
        officerTypeName: {
            type: 'string',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        name: {
            type: 'string',
            nullable: true
        },
        dateOfBirth: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        placeOfBirth: {
            type: 'string',
            nullable: true
        },
        countryOfBirth: {
            type: 'string',
            nullable: true
        },
        countryCodeOfBirth: {
            type: 'string',
            nullable: true
        },
        nationality: {
            type: 'string',
            nullable: true
        },
        residentialAddress: {
            type: 'string',
            nullable: true
        },
        incorporationNumber: {
            type: 'string',
            nullable: true
        },
        dateOfIncorporation: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        address: {
            type: 'string',
            nullable: true
        },
        countryOfFormation: {
            type: 'string',
            nullable: true
        },
        jurisdictionOfRegulator: {
            type: 'string',
            nullable: true
        },
        nameOfRegulator: {
            type: 'string',
            nullable: true
        },
        sovereignState: {
            type: 'string',
            nullable: true
        },
        tin: {
            type: 'string',
            nullable: true
        },
        stockCode: {
            type: 'string',
            nullable: true
        },
        stockExchange: {
            type: 'string',
            nullable: true
        },
        metaData: {
            '$ref': '#/components/schemas/LegalEntityRelationMetaData'
        }
    },
    additionalProperties: false
} as const;

export const StartPaymentResponseSchema = {
    type: 'object',
    properties: {
        transactionId: {
            type: 'string',
            format: 'uuid'
        },
        providerTransactionId: {
            type: 'string',
            nullable: true
        },
        callBackUrl: {
            type: 'string',
            nullable: true
        },
        result: {
            type: 'integer',
            format: 'int32'
        },
        resultText: {
            type: 'string',
            nullable: true
        },
        resultNumber: {
            type: 'integer',
            format: 'int32'
        },
        htmlContent: {
            type: 'string',
            nullable: true
        },
        consumer: {
            '$ref': '#/components/schemas/ConsumerModel'
        },
        provider: {
            '$ref': '#/components/schemas/ProviderModel'
        }
    },
    additionalProperties: false
} as const;

export const SubmissionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        submittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        statusText: {
            type: 'string',
            nullable: true,
            deprecated: true
        },
        createdByEmail: {
            type: 'string',
            nullable: true
        },
        formDocument: {
            '$ref': '#/components/schemas/FormDocumentWithRevisionsDTO'
        },
        isPaid: {
            type: 'boolean',
            nullable: true
        },
        paidAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reportId: {
            type: 'string',
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        layout: {
            type: 'string',
            nullable: true
        },
        exportedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        legalEntityVPCode: {
            type: 'string',
            nullable: true
        },
        legalEntityReferralOffice: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        paymentMethod: {
            type: 'string',
            nullable: true
        },
        paymentReceivedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        paymentReference: {
            type: 'string',
            nullable: true
        },
        invoiceId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        startsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        endsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        documentIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        initialSubmittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenRequestComments: {
            type: 'string',
            nullable: true
        },
        requestsForInformation: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionRFIDTO'
            },
            nullable: true
        },
        legalEntityVPStatus: {
            type: 'string',
            nullable: true
        },
        legalEntityVPSubStatus: {
            type: 'string',
            nullable: true
        },
        isDeleted: {
            type: 'boolean'
        },
        deletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SubmissionDTOResponseTypesSchema = {
    type: 'object',
    properties: {
        value: {
            nullable: true,
            readOnly: true
        },
        index: {
            type: 'integer',
            format: 'int32',
            readOnly: true
        },
        isT0: {
            type: 'boolean',
            readOnly: true
        },
        isT1: {
            type: 'boolean',
            readOnly: true
        },
        asT0: {
            '$ref': '#/components/schemas/SubmissionDTO'
        },
        asT1: {
            '$ref': '#/components/schemas/SubmissionKeyValueDTO'
        }
    },
    additionalProperties: false,
    description: 'Submission response types DTO.'
} as const;

export const SubmissionDataRequestDTOSchema = {
    required: ['submissionIds'],
    type: 'object',
    properties: {
        submissionIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            }
        }
    },
    additionalProperties: false
} as const;

export const SubmissionDataSetDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        dataSet: {
            type: 'object',
            additionalProperties: {
                type: 'string',
                nullable: true
            },
            nullable: true
        },
        documentIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SubmissionKeyValueDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        createdAt: {
            type: 'string',
            format: 'date-time'
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        submittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        statusText: {
            type: 'string',
            nullable: true,
            deprecated: true
        },
        createdByEmail: {
            type: 'string',
            nullable: true
        },
        formDocument: {
            '$ref': '#/components/schemas/FormDocumentWithRevisionsKeyValueDTO'
        },
        isPaid: {
            type: 'boolean',
            nullable: true
        },
        paidAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reportId: {
            type: 'string',
            nullable: true
        },
        moduleId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        legalEntityId: {
            type: 'string',
            format: 'uuid'
        },
        layout: {
            type: 'string',
            nullable: true
        },
        exportedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        legalEntityName: {
            type: 'string',
            nullable: true
        },
        legalEntityCode: {
            type: 'string',
            nullable: true
        },
        legalEntityVPCode: {
            type: 'string',
            nullable: true
        },
        legalEntityReferralOffice: {
            type: 'string',
            nullable: true
        },
        masterClientCode: {
            type: 'string',
            nullable: true
        },
        paymentMethod: {
            type: 'string',
            nullable: true
        },
        paymentReceivedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        paymentReference: {
            type: 'string',
            nullable: true
        },
        invoiceId: {
            type: 'string',
            format: 'uuid',
            nullable: true
        },
        startsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        endsAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        documentIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        initialSubmittedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        reopenRequestComments: {
            type: 'string',
            nullable: true
        },
        requestsForInformation: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionRFIDTO'
            },
            nullable: true
        },
        legalEntityVPStatus: {
            type: 'string',
            nullable: true
        },
        legalEntityVPSubStatus: {
            type: 'string',
            nullable: true
        },
        isDeleted: {
            type: 'boolean'
        },
        deletedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false,
    description: `Version of NetProGroup.Trust.Application.Contracts.Submissions.SubmissionDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.FormDocumentWithRevisionsKeyValueDTO form document.
Intended to generate correct OpenAPI documentation.`
} as const;

export const SubmissionPaidStatusDtoSchema = {
    type: 'object',
    properties: {
        companyVPCode: {
            type: 'string',
            nullable: true
        },
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        isPaid: {
            type: 'boolean',
            nullable: true
        },
        submissionAvailable: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const SubmissionRFIDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        submissionId: {
            type: 'string',
            format: 'uuid'
        },
        deadLine: {
            type: 'string',
            format: 'date-time'
        },
        comments: {
            type: 'string',
            nullable: true
        },
        status: {
            '$ref': '#/components/schemas/RequestForInformationStatus'
        },
        cancellationReason: {
            type: 'string',
            nullable: true
        },
        documents: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionRFIDocumentDTO'
            },
            nullable: true,
            readOnly: true
        }
    },
    additionalProperties: false
} as const;

export const SubmissionRFIDetailsDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        status: {
            '$ref': '#/components/schemas/SubmissionStatus'
        },
        requestsForInformation: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionRFIDTO'
            },
            nullable: true
        },
        activityLogItems: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/ActivityLogItemDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SubmissionRFIDocumentDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        documentId: {
            type: 'string',
            format: 'uuid'
        },
        document: {
            '$ref': '#/components/schemas/DocumentDTO'
        },
        requestForInformationId: {
            type: 'string',
            format: 'uuid'
        },
        createdByManagement: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const SubmissionSettingsDTOSchema = {
    type: 'object',
    properties: {
        firstSubmissionYear: {
            type: 'integer',
            format: 'int32',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SubmissionStatusSchema = {
    enum: ['Draft', 'Revision', 'Scheduled', 'Submitted', 'Paid', 'Temporal', 'InformationRequested'],
    type: 'string'
} as const;

export const SubmissionsPaidStatusResponseSchema = {
    type: 'object',
    properties: {
        paidStatuses: {
            type: 'array',
            items: {
                '$ref': '#/components/schemas/SubmissionPaidStatusDto'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const SubmitPaymentRequestDTOSchema = {
    required: ['tokenId', 'transactionId'],
    type: 'object',
    properties: {
        transactionId: {
            type: 'string',
            format: 'uuid'
        },
        tokenId: {
            maxLength: 250,
            minLength: 0,
            type: 'string'
        }
    },
    additionalProperties: false
} as const;

export const SubmitPaymentResponseDTOSchema = {
    type: 'object',
    properties: {
        transactionId: {
            type: 'string',
            format: 'uuid'
        },
        providerTransactionId: {
            type: 'string',
            nullable: true
        },
        result: {
            type: 'integer',
            format: 'int32'
        },
        resultText: {
            type: 'string',
            nullable: true
        },
        resultNumber: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const SubmitSubmissionDTOSchema = {
    type: 'object',
    properties: {
        submissionId: {
            type: 'string',
            format: 'uuid'
        },
        scheduleSubmit: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const SyncDetailsDTOSchema = {
    type: 'object',
    properties: {
        lastSuccessfulSync: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        jurisdictionsUsed: {
            type: 'array',
            items: {
                type: 'string'
            },
            nullable: true
        },
        updatedCount: {
            type: 'integer',
            format: 'int32'
        },
        deletedCount: {
            type: 'integer',
            format: 'int32'
        }
    },
    additionalProperties: false
} as const;

export const SyncStatusSchema = {
    enum: ['Inactive', 'Active'],
    type: 'string'
} as const;

export const TermsConditionsStatusDTOSchema = {
    type: 'object',
    properties: {
        isAccepted: {
            type: 'boolean'
        },
        version: {
            type: 'string',
            nullable: true
        },
        acceptedAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const UnprocessedRecordDTOSchema = {
    type: 'object',
    properties: {
        entityType: {
            type: 'string',
            nullable: true
        },
        identifier: {
            type: 'string',
            nullable: true
        },
        reason: {
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const UpdateCompanyAnnualFeeStatusDTOSchema = {
    type: 'object',
    properties: {
        financialYear: {
            type: 'integer',
            format: 'int32'
        },
        isPaid: {
            type: 'boolean'
        },
        legalEntityIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const UpdateFormTemplateVersionDTOSchema = {
    type: 'object',
    properties: {
        id: {
            type: 'string',
            format: 'uuid'
        },
        name: {
            type: 'string',
            nullable: true
        },
        version: {
            type: 'string',
            nullable: true
        },
        startDate: {
            type: 'string',
            format: 'date-time',
            nullable: true
        },
        year: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        formBuilder: {
            '$ref': '#/components/schemas/FormBuilder'
        }
    },
    additionalProperties: false
} as const;

export const UpdateSubmissionInformationDTOSchema = {
    type: 'object',
    properties: {
        startAt: {
            type: 'string',
            format: 'date-time'
        },
        endAt: {
            type: 'string',
            format: 'date-time'
        }
    },
    additionalProperties: false
} as const;

export const UserInvitationDetailsDTOSchema = {
    type: 'object',
    properties: {
        isInvited: {
            type: 'boolean'
        },
        lastInvitationAt: {
            type: 'string',
            format: 'date-time',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const UserMasterClientsDTOSchema = {
    type: 'object',
    properties: {
        userId: {
            type: 'string',
            format: 'uuid'
        },
        masterClientIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'uuid'
            },
            nullable: true
        },
        removeUnmentionedMasterClients: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const UserPermissionDTOSchema = {
    type: 'object',
    properties: {
        permissionName: {
            enum: ['announcements.search', 'announcements.view', 'announcements.delete', 'announcements.create-limited', 'announcements.create', 'bfr.panama.submissions.view', 'bfr.panama.submissions.search', 'bfr.panama.submissions.export', 'bfr.panama.submissions.reset', 'bfr.panama.submissions.view-paid', 'bfr.panama.submissions.mark-paid', 'bfr.panama.invoices.export', 'bfr.panama.rfi-request.start', 'bo-dir.search', 'bo-dir.view', 'bo-dir.export', 'companies.onboarding.access', 'companies.onboarding.approve', 'companies.onboarding.reject', 'companies.search', 'companies.view', 'companies.modules.available.view', 'companies.modules.available.set', 'companies.custom-str-fee.view', 'companies.custom-str-fee.set', 'companies.late-payment-exemption-str.set', 'companies.custom-bfr-fee.view', 'companies.custom-bfr-fee.set', 'companies.annual-fee.view', 'companies.annual-fee.set', 'companies.log.view', 'companies.first-submission-year.view', 'companies.first-submission-year.set', 'companies.submissions.move-delete', 'companies.onboarding-status.reset', 'companies.str-submission.log.view', 'companies.inactive-to-file-submission.set', 'companies.delete-all', 'companies.delete-draft', 'es.bahamas.submissions.view', 'es.bahamas.submissions.search', 'es.bahamas.submissions.export', 'es.bahamas.submissions.reset', 'es.bahamas.submissions.delete-completed', 'es.bahamas.submissions.delete-saved', 'es.bahamas.submissions.view-paid', 'es.bahamas.submissions.mark-paid', 'es.bahamas.payments.import', 'es.bahamas.submissions.export.ita', 'es.bahamas.invoices.export', 'es.bahamas.companies.custom-es-fee.view', 'es.bahamas.companies.custom-es-fee.set', 'es.bahamas.financial-period.set', 'es.bahamas.data-migration', 'es.bahamas.rfi-request.start', 'es.bvi.submissions.view', 'es.bvi.submissions.search', 'es.bvi.submissions.export', 'es.bvi.submissions.reset', 'es.bvi.submissions.delete-completed', 'es.bvi.submissions.delete-saved', 'es.bvi.submissions.view-paid', 'es.bvi.submissions.mark-paid', 'es.bvi.payments.import', 'es.bvi.submissions.export.ita', 'es.bvi.invoices.export', 'es.bvi.rfi-request.start', 'es.bvi.companies.custom-es-fee.view', 'es.bvi.companies.custom-es-fee.set', 'masterclients.search', 'masterclients.view', 'masterclients.send-invitation', 'masterclients.trident-users.view', 'masterclients.trident-users.add', 'masterclients.trident-users.remove', 'masterclients.log.view', 'rfi.view', 'rfi.complete', 'rfi.start', 'rfi.cancel', 'general', 'general.status-page-sync', 'str.submissions.view', 'str.submissions.search', 'str.submissions.export', 'str.submissions.reset', 'str.submissions.view-paid', 'str.submissions.mark-paid', 'str.payments.import', 'str.submissions.export.ird', 'str.invoices.export', 'str.invoices.export.financial-report', 'str.management-information', 'str.fee.view', 'str.fee.set', 'str.late-payments.view', 'str.late-payments.set', 'str.data-migration', 'str.late-payments.edit', 'str.submission-log.view', 'str.rfi-request.start', 'users.search', 'users.view', 'users.block', 'users.unblock', 'users.reset-authentication', 'users.view-log'],
            type: 'string',
            nullable: true
        }
    },
    additionalProperties: false
} as const;

export const ValidationProblemDetailsSchema = {
    type: 'object',
    properties: {
        type: {
            type: 'string',
            nullable: true
        },
        title: {
            type: 'string',
            nullable: true
        },
        status: {
            type: 'integer',
            format: 'int32',
            nullable: true
        },
        detail: {
            type: 'string',
            nullable: true
        },
        instance: {
            type: 'string',
            nullable: true
        },
        errors: {
            type: 'object',
            additionalProperties: {
                type: 'array',
                items: {
                    type: 'string'
                }
            },
            nullable: true
        }
    },
    additionalProperties: {}
} as const;

export const VerifyMFACodeResultDTOSchema = {
    type: 'object',
    properties: {
        verificationCode: {
            type: 'string',
            nullable: true
        },
        success: {
            type: 'boolean'
        }
    },
    additionalProperties: false
} as const;

export const ViewPointSyncStatusDTOSchema = {
    type: 'object',
    properties: {
        syncDetails: {
            type: 'object',
            additionalProperties: {
                '$ref': '#/components/schemas/SyncDetailsDTO'
            },
            nullable: true
        }
    },
    additionalProperties: false
} as const;