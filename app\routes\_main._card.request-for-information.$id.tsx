import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Separator } from "@netpro/design-system";
import { Link, Outlet, useLoaderData, useNavigate } from "@remix-run/react";
import { ArrowDownToLine, Bell, ChevronLeft, FileCheck2, FileSearch, FileText, User } from "lucide-react";
import { type ReactNode, useMemo } from "react";
import { CardContainer } from "~/components/CardContainer";
import { DetailRow } from "~/features/rfi/components/DetailRow";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { formatDate } from "~/lib/utilities/format";
import { managementGetRfi } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "RFI Details",
    to: "/request-for-information",
  },
  title: "RFI Details",
}

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);

  const { id } = params;
  if (!id) {
    setNotification({ title: "RFI ID is required", variant: "error" });

    return redirect("/request-for-information");
  }

  const rfiResponse = await managementGetRfi({ headers: await authHeaders(request), path: { requestForInformationId: id } });
  if (!rfiResponse.data) {
    setNotification({ title: "The requested RFI could not be found", variant: "error" });

    return redirect("/request-for-information");
  }

  return json({ rfiDetails: rfiResponse.data });
}, { authorize: ["rfi.view"] });

export default function RFIDetailsPage(): ReactNode {
  const navigate = useNavigate();
  const { rfiDetails } = useLoaderData<typeof loader>();
  const getStatusBadgeVariant = (status: string | undefined) => {
    switch (status) {
      case "Completed":
        return "success" as const;
      case "Active":
        return "warning" as const;
      case "Cancelled":
        return "danger" as const;
      case "Draft":
        return "info" as const;
      default:
        return "secondary" as const;
    }
  };
  const clientResponseDocuments = useMemo(() => rfiDetails.documents?.filter(doc => !doc.createdByManagement), [rfiDetails.documents]);
  const officerRequestDocuments = useMemo(() => rfiDetails.documents?.filter(doc => doc.createdByManagement), [rfiDetails.documents]);
  const rfiDueInDays = useMemo(() => {
    if (!rfiDetails.deadLine) {
      return 0;
    }

    const now = new Date();
    const dueDate = new Date(rfiDetails.deadLine);
    const diffTime = dueDate.getTime() - now.getTime();

    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }, [rfiDetails.deadLine]);

  return (
    <CardContainer>
      <div className="flex flex-col gap-4 px-5 py-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <CardTitle className="text-2xl font-semibold text-blue-700">
                      {rfiDetails.legalEntityName}
                    </CardTitle>
                    <Badge variant={getStatusBadgeVariant(rfiDetails.status)}>
                      {rfiDetails.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600 ">Master Client</span>
                      <span className="font-bold text-gray-800">{rfiDetails.masterClientCode}</span>
                    </div>
                    <Separator orientation="vertical" className="h-4" />
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600">Jurisdiction</span>
                      <span className="font-bold text-gray-800">{rfiDetails.jurisdictionName}</span>
                    </div>
                    <Separator orientation="vertical" className="h-4" />
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-600">Regulatory Code</span>
                      <span className="font-bold text-gray-800">{rfiDetails.legalEntityCode}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600">RFI Deadline</span>
                  <span className="font-bold text-gray-800">{formatDate(rfiDetails.deadLine, { timezone: "UTC", formatStr: "dd-MMM-yyyy" })}</span>
                </div>
                <Separator orientation="vertical" className="h-4" />
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600">RFI Due In Days</span>
                  <span className="font-bold text-gray-800">
                    {rfiDueInDays}
                  </span>
                  {rfiDueInDays <= 0 && (
                    <Badge variant="danger" className="text-xs">
                      Overdue
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Bell className="size-5 text-blue-600" />
                <CardTitle className="text-lg font-semibold">Reminders Sent</CardTitle>
              </div>
            </CardHeader>
            {/*
              * TODO: Implement reminders list
              <CardContent className="space-y-1 ml-7">
                {rfiDetails.reminders.map((reminder, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div key={`reminder-${reminder.type}-${index}`} className="flex items-center gap-1 text-sm">
                    <span className="font-medium">{reminder.type}</span>
                    <span className="text-gray-600">
                      on
                      {" "}
                      {formatDate(reminder.date, { timezone: "UTC", formatStr: "dd-MMM-yyyy" })}
                    </span>
                  </div>
                ))}
              </CardContent>
            */}
          </Card>
        </div>
        {/* Completed Section */}
        {rfiDetails.status === "Completed" && rfiDetails.completedAt && (
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <FileCheck2 className="size-5 text-blue-600" />
                <CardTitle className="text-lg font-semibold">Completed</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-2 ml-7">
              <div className="space-y-3">
                <DetailRow label="Date">
                  {formatDate(rfiDetails.completedAt, { timezone: "UTC", formatStr: "dd-MMM-yyyy HH:mm" })}
                  {" "}
                  (UTC)
                </DetailRow>

                <DetailRow label="Completed By">
                  {rfiDetails.completedBy}
                </DetailRow>

                <DetailRow label="Date Submitted to Regulator">
                  {formatDate(rfiDetails.completedAt, { timezone: "UTC", formatStr: "dd-MMM-yyyy" })}
                </DetailRow>

                <DetailRow label="Remark">
                  {rfiDetails.remark}
                </DetailRow>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Cancelled Section */}
        {rfiDetails.status === "Cancelled" && rfiDetails.completedAt && (
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <FileText className="size-5 text-blue-600" />
                <CardTitle className="text-lg font-semibold">Cancelled</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-2 ml-7">
              <div className="space-y-3">
                <DetailRow label="RFI Cancelled Date">
                  {formatDate(rfiDetails.completedAt, { timezone: "UTC", formatStr: "dd-MMM-yyyy HH:mm" })}
                  {" "}
                  (UTC)
                </DetailRow>

                <DetailRow label="Cancelled by">
                  {rfiDetails.completedBy}
                </DetailRow>

                <DetailRow label="Reason">
                  {rfiDetails.remark}
                </DetailRow>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Client Response Section */}
        {rfiDetails.repliedAt && (
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <User className="size-5 text-blue-600" />
                <CardTitle className="text-lg font-semibold">Client Response</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-2 ml-7">
              <div className="space-y-3">
                <DetailRow label="Date">
                  {formatDate(rfiDetails.repliedAt, { timezone: "UTC", formatStr: "dd-MMM-yyyy HH:mm" })}
                  {" "}
                  (UTC)
                </DetailRow>

                <DetailRow label="Completed By">
                  {rfiDetails.repliedBy}
                </DetailRow>

                <DetailRow label="Client Message">
                  {rfiDetails.response}
                </DetailRow>

                {clientResponseDocuments && clientResponseDocuments.length > 0 && (
                  <DetailRow label="Supporting Documents">
                    <div className="space-y-2">
                      {clientResponseDocuments.map(doc => (
                        <div key={`client-doc-${doc.id}`} className="flex items-center gap-2">
                          <ArrowDownToLine className="size-5" />
                          <Link
                            to="#"
                            className="hover:text-blue-800 text-sm hover:underline"
                          >
                            {doc.document?.filename}
                          </Link>
                        </div>
                      ))}
                    </div>
                  </DetailRow>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Officer Request Section */}
        {rfiDetails.createdAt && (
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <FileSearch className="size-5 text-blue-600" />
                <CardTitle className="text-lg font-semibold">Officer Request</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-2 ml-7">
              <div className="space-y-3">
                <DetailRow label="Start Date">
                  {formatDate(rfiDetails.createdAt, { timezone: "UTC", formatStr: "dd-MMM-yyyy HH:mm" })}
                  {" "}
                  (UTC)
                </DetailRow>

                <DetailRow label="Request Initiator">
                  {rfiDetails.createdBy}
                </DetailRow>

                <DetailRow label="Reason For RFI Request">
                  {rfiDetails.requestComment}
                </DetailRow>

                {officerRequestDocuments && officerRequestDocuments.length > 0 && (
                  <DetailRow label="Supporting Documents">
                    <div className="space-y-2">
                      {officerRequestDocuments.map(doc => (
                        <div key={`officer-doc-${doc.id}`} className="flex items-center gap-2">
                          <ArrowDownToLine className="size-5" />
                          {/* TODO: Implement document download link */}
                          <Link
                            to="#"
                            className="hover:text-blue-800 text-sm hover:underline"
                          >
                            {doc.document?.filename}
                          </Link>
                        </div>
                      ))}
                    </div>
                  </DetailRow>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      {/* Buttons */}
      <div className="flex justify-between  px-5 pt-4">
        <Button variant="outline" asChild className="place-self-start pl-2">
          <Link to="/request-for-information">
            <ChevronLeft className="text-primary ml-0" />
            Back to Request For Information
          </Link>
        </Button>
        <div className="flex gap-2">
          <Button className="justify-start gap-2" variant="destructive" onClick={() => navigate("cancel")}>
            Cancel RFI
          </Button>
          <Button className="justify-start gap-2" onClick={() => navigate("complete")}>
            Mark as Complete
          </Button>
        </div>
      </div>
      {/* Dialogs */}
      <Outlet />
    </CardContainer>

  );
}
